import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import pickle
from coordinate_normalization import process_data_with_normalization

# ANN 正規化模型類別定義
class ANN_Normalized(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, output_dim)
        )

    def forward(self, x):
        return self.model(x)

def load_normalized_model():
    """載入正規化模型"""
    print("🔄 載入正規化 ANN 模型...")
    
    # 載入模型配置信息
    with open("saved_models_normalized/ann_normalized_info.pkl", "rb") as f:
        model_info = pickle.load(f)
    
    # 載入標籤編碼器和標準化器
    with open("saved_models_normalized/ann_normalized_label_encoder.pkl", "rb") as f:
        label_encoder = pickle.load(f)
    
    with open("saved_models_normalized/ann_normalized_scaler.pkl", "rb") as f:
        scaler = pickle.load(f)
    
    # 重建模型結構
    model = ANN_Normalized(model_info["input_dim"], model_info["output_dim"])
    
    # 載入模型權重
    model.load_state_dict(torch.load("saved_models_normalized/ann_normalized_weights.pth"))
    model.eval()
    
    print(f"✅ 正規化模型載入成功！測試準確率: {model_info['test_accuracy']:.4f}")
    print(f"   特徵維度: {model_info['input_dim']}")
    print(f"   包含相對特徵: {model_info.get('features_include_relative', False)}")
    print(f"   正規化方法: {model_info.get('normalization_method', 'unknown')}")
    
    return model, label_encoder, scaler, model_info

def predict_2xlsx_normalized():
    """使用正規化模型對 2.xlsx 進行預測"""
    print(f"\n📊 使用正規化方法處理 2.xlsx...")
    
    # 使用座標正規化處理 2.xlsx
    X, df_processed = process_data_with_normalization("2.xlsx", has_labels=False)
    print(f"正規化後特徵維度: {X.shape}")
    
    # 載入正規化模型
    model, label_encoder, scaler, model_info = load_normalized_model()
    
    # 標準化特徵 (使用訓練時的 scaler)
    print(f"\n🎯 使用正規化模型進行預測...")
    X_scaled = scaler.transform(X)
    X_tensor = torch.tensor(X_scaled, dtype=torch.float32)
    
    # 預測
    with torch.no_grad():
        predictions = model(X_tensor)
        predicted_labels = predictions.argmax(dim=1)
        predicted_names = label_encoder.inverse_transform(predicted_labels.numpy())
        confidence = torch.softmax(predictions, dim=1).max(dim=1)[0]
    
    # 讀取原始時間信息
    df_original = pd.read_excel("2.xlsx")
    
    # 創建結果 DataFrame
    results_df = pd.DataFrame({
        '幀數': df_original['幀數'],
        '影片時間': df_original['影片時間'],
        '正規化預測': predicted_names,
        '信心度': confidence.numpy()
    })
    
    # 保存預測結果
    results_df.to_excel("2xlsx_normalized_predictions.xlsx", index=False)
    print(f"\n💾 正規化預測結果已保存到 '2xlsx_normalized_predictions.xlsx'")
    
    # 顯示統計信息
    print(f"\n📈 正規化預測結果統計:")
    print(f"總數據筆數: {len(results_df)}")
    
    print(f"\n正規化模型預測分布:")
    pred_counts = pd.Series(predicted_names).value_counts()
    for action, count in pred_counts.head(10).items():
        avg_confidence = results_df[results_df['正規化預測'] == action]['信心度'].mean()
        print(f"  {action}: {count} 筆 ({count/len(results_df)*100:.1f}%) - 平均信心度: {avg_confidence:.3f}")
    
    # 顯示前20筆預測結果
    print(f"\n🔍 前20筆正規化預測結果:")
    print("-" * 80)
    print(f"{'幀數':<6} {'時間':<12} {'正規化預測':<20} {'信心度':<8}")
    print("-" * 80)
    
    for i in range(min(20, len(results_df))):
        row = results_df.iloc[i]
        print(f"{row['幀數']:<6} {row['影片時間']:<12} {row['正規化預測']:<20} {row['信心度']:<8.3f}")
    
    # 計算高信心度預測比例
    high_confidence = (confidence > 0.8).sum().item()
    high_confidence_rate = high_confidence / len(results_df) * 100
    print(f"\n🎯 高信心度預測 (>0.8): {high_confidence}/{len(results_df)} ({high_confidence_rate:.1f}%)")
    
    return results_df

def compare_with_original_predictions():
    """比較原始模型和正規化模型的預測結果"""
    try:
        # 讀取原始預測結果
        original_df = pd.read_excel("2xlsx_predictions.xlsx")
        normalized_df = pd.read_excel("2xlsx_normalized_predictions.xlsx")
        
        print(f"\n🔄 比較原始模型 vs 正規化模型:")
        print("-" * 80)
        print(f"{'幀數':<6} {'原始ANN':<15} {'正規化ANN':<15} {'一致性':<8}")
        print("-" * 80)
        
        agreement_count = 0
        for i in range(min(20, len(original_df))):
            original_pred = original_df.iloc[i]['ANN預測']
            normalized_pred = normalized_df.iloc[i]['正規化預測']
            agreement = "✓" if original_pred == normalized_pred else "✗"
            if original_pred == normalized_pred:
                agreement_count += 1
            
            print(f"{i:<6} {original_pred:<15} {normalized_pred:<15} {agreement:<8}")
        
        total_agreement = (original_df['ANN預測'] == normalized_df['正規化預測']).sum()
        agreement_rate = total_agreement / len(original_df) * 100
        print(f"\n🤝 整體預測一致性: {total_agreement}/{len(original_df)} ({agreement_rate:.1f}%)")
        
    except FileNotFoundError:
        print("\n⚠️  未找到原始預測結果檔案，跳過比較")

if __name__ == "__main__":
    print("🚀 使用正規化模型對 2.xlsx 進行預測")
    print("=" * 60)
    
    try:
        # 進行正規化預測
        results = predict_2xlsx_normalized()
        
        # 比較預測結果
        compare_with_original_predictions()
        
        print(f"\n✅ 正規化預測完成！")
        print(f"\n🎯 正規化的優勢:")
        print(f"   ✅ 消除位置偏移影響")
        print(f"   ✅ 身體尺度正規化")
        print(f"   ✅ 添加相對特徵")
        print(f"   ✅ 更好的泛化能力")
        
    except FileNotFoundError as e:
        print(f"❌ 檔案未找到: {e}")
        print("請確認已訓練並保存正規化模型")
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        import traceback
        traceback.print_exc()

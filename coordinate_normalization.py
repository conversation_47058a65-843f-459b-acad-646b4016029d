import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

def normalize_pose_coordinates(df):
    """
    正規化人體姿態座標，消除位置偏移影響
    """
    print("🔄 開始座標正規化處理...")
    
    # 複製數據避免修改原始數據
    df_normalized = df.copy()
    
    # 獲取所有座標欄位
    x_cols = [col for col in df.columns if '_X座標' in col]
    y_cols = [col for col in df.columns if '_Y座標' in col]
    z_cols = [col for col in df.columns if '_Z座標' in col]
    
    print(f"找到 {len(x_cols)} 個 X 座標, {len(y_cols)} 個 Y 座標, {len(z_cols)} 個 Z 座標")
    
    # 方法1: 以身體中心點（肩膀中點）為基準進行相對座標轉換
    if '左肩膀_X座標' in df.columns and '右肩膀_X座標' in df.columns:
        print("📍 使用肩膀中點作為參考點進行正規化...")
        
        # 計算肩膀中點作為身體中心
        center_x = (df['左肩膀_X座標'] + df['右肩膀_X座標']) / 2
        center_y = (df['左肩膀_Y座標'] + df['右肩膀_Y座標']) / 2
        center_z = (df['左肩膀_Z座標'] + df['右肩膀_Z座標']) / 2
        
        # 將所有座標轉換為相對於身體中心的座標
        for col in x_cols:
            df_normalized[col] = df[col] - center_x
        for col in y_cols:
            df_normalized[col] = df[col] - center_y
        for col in z_cols:
            df_normalized[col] = df[col] - center_z
            
    # 方法2: 以鼻子為基準（如果肩膀不可用）
    elif '鼻子_X座標' in df.columns:
        print("📍 使用鼻子位置作為參考點進行正規化...")
        
        center_x = df['鼻子_X座標']
        center_y = df['鼻子_Y座標'] 
        center_z = df['鼻子_Z座標']
        
        for col in x_cols:
            df_normalized[col] = df[col] - center_x
        for col in y_cols:
            df_normalized[col] = df[col] - center_y
        for col in z_cols:
            df_normalized[col] = df[col] - center_z
    
    # 方法3: 身體尺度正規化（使用肩膀距離）
    if '左肩膀_X座標' in df.columns and '右肩膀_X座標' in df.columns:
        print("📏 進行身體尺度正規化...")
        
        # 計算肩膀距離作為身體尺度參考
        shoulder_distance = np.sqrt(
            (df['左肩膀_X座標'] - df['右肩膀_X座標'])**2 + 
            (df['左肩膀_Y座標'] - df['右肩膀_Y座標'])**2 + 
            (df['左肩膀_Z座標'] - df['右肩膀_Z座標'])**2
        )
        
        # 避免除以零
        shoulder_distance = shoulder_distance.replace(0, 1)
        
        # 將所有座標除以肩膀距離進行尺度正規化
        for col in x_cols + y_cols + z_cols:
            df_normalized[col] = df_normalized[col] / shoulder_distance
    
    print("✅ 座標正規化完成")
    return df_normalized

def add_relative_features(df):
    """
    添加相對特徵（關節間距離、角度等）
    """
    print("🔄 計算相對特徵...")
    
    df_enhanced = df.copy()
    
    # 計算重要關節間的距離
    joint_pairs = [
        ('左肩膀', '右肩膀'),
        ('左手腕', '右手腕'),
        ('左膝蓋', '右膝蓋'),
        ('左腳踝', '右腳踝'),
        ('左肩膀', '左手腕'),
        ('右肩膀', '右手腕'),
        ('左臀部', '左膝蓋'),
        ('右臀部', '右膝蓋')
    ]
    
    for joint1, joint2 in joint_pairs:
        x1_col = f'{joint1}_X座標'
        y1_col = f'{joint1}_Y座標'
        z1_col = f'{joint1}_Z座標'
        x2_col = f'{joint2}_X座標'
        y2_col = f'{joint2}_Y座標'
        z2_col = f'{joint2}_Z座標'
        
        if all(col in df.columns for col in [x1_col, y1_col, z1_col, x2_col, y2_col, z2_col]):
            distance = np.sqrt(
                (df[x1_col] - df[x2_col])**2 + 
                (df[y1_col] - df[y2_col])**2 + 
                (df[z1_col] - df[z2_col])**2
            )
            df_enhanced[f'{joint1}_{joint2}_距離'] = distance
    
    print(f"✅ 添加了 {len(joint_pairs)} 個相對距離特徵")
    return df_enhanced

def process_data_with_normalization(file_path, has_labels=True):
    """
    處理數據並進行座標正規化
    """
    print(f"📂 處理檔案: {file_path}")
    
    # 讀取數據
    df = pd.read_excel(file_path)
    print(f"原始數據形狀: {df.shape}")
    
    # 座標正規化
    df_normalized = normalize_pose_coordinates(df)
    
    # 添加相對特徵
    df_enhanced = add_relative_features(df_normalized)
    
    # 移除非數值欄位
    if has_labels:
        non_numeric_cols = ['幀數', '影片時間', '標籤']
    else:
        non_numeric_cols = ['幀數', '影片時間', '影片秒數']
    
    # 檢查哪些欄位實際存在
    existing_non_numeric = [col for col in non_numeric_cols if col in df_enhanced.columns]
    
    # 提取特徵和標籤
    if has_labels:
        X = df_enhanced.drop(existing_non_numeric, axis=1).values
        y = df['標籤'].values if '標籤' in df.columns else None
        return X, y, df_enhanced
    else:
        X = df_enhanced.drop(existing_non_numeric, axis=1).values
        return X, df_enhanced

if __name__ == "__main__":
    print("🚀 座標正規化測試")
    print("=" * 50)
    
    # 測試 1.xlsx
    print("\n測試 1.xlsx:")
    X1, y1, df1_processed = process_data_with_normalization("1.xlsx", has_labels=True)
    print(f"處理後特徵維度: {X1.shape}")
    
    # 測試 2.xlsx  
    print("\n測試 2.xlsx:")
    X2, df2_processed = process_data_with_normalization("2.xlsx", has_labels=False)
    print(f"處理後特徵維度: {X2.shape}")
    
    # 比較特徵統計
    print(f"\n📊 特徵統計比較:")
    print(f"1.xlsx 前5個特徵平均值: {X1[:, :5].mean(axis=0)}")
    print(f"2.xlsx 前5個特徵平均值: {X2[:, :5].mean(axis=0)}")
    
    print(f"\n1.xlsx 前5個特徵標準差: {X1[:, :5].std(axis=0)}")
    print(f"2.xlsx 前5個特徵標準差: {X2[:, :5].std(axis=0)}")
    
    print("\n✅ 座標正規化測試完成！")

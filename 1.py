# SOP作業分析程式碼套件
# 用於分析MediaPipe提取的手部追蹤數據

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from scipy import stats
from scipy.signal import savgol_filter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft JhengHei']
plt.rcParams['axes.unicode_minus'] = False

class SOPAnalyzer:
    def __init__(self, csv_file_path):
        """
        初始化SOP分析器
        
        Parameters:
        csv_file_path (str): MediaPipe數據CSV檔案路徑
        
        預期CSV格式:
        - frame: 影格編號
        - timestamp: 時間戳記
        - hand_landmark_x_0~20: 手部21個關鍵點的x座標
        - hand_landmark_y_0~20: 手部21個關鍵點的y座標
        - hand_landmark_z_0~20: 手部21個關鍵點的z座標
        """
        self.data = pd.read_csv(csv_file_path)
        self.processed_data = None
        self.sequences = None
        self.wrist_data = None
        self.fingertip_data = None
        self.tool_data = None
        
    def preprocess_data(self):
        """預處理數據"""
        print("開始預處理數據...")
        
        # 提取手腕關鍵點 (landmark 0)
        self.wrist_data = self.data[['frame', 'timestamp', 
                                   'hand_landmark_x_0', 
                                   'hand_landmark_y_0', 
                                   'hand_landmark_z_0']].copy()
        self.wrist_data.columns = ['frame', 'timestamp', 'x', 'y', 'z']
        
        # 提取食指指尖 (landmark 8) - 假設握持工具
        self.fingertip_data = self.data[['frame', 'timestamp',
                                       'hand_landmark_x_8',
                                       'hand_landmark_y_8', 
                                       'hand_landmark_z_8']].copy()
        self.fingertip_data.columns = ['frame', 'timestamp', 'x', 'y', 'z']
        
        # 計算工具方向 (手腕到食指的向量)
        self.tool_data = self.calculate_tool_orientation()
        
        print("數據預處理完成")
        
    def calculate_tool_orientation(self):
        """計算工具方向和角度"""
        tool_data = []
        
        for i in range(len(self.wrist_data)):
            # 計算手腕到食指的向量
            dx = self.fingertip_data.iloc[i]['x'] - self.wrist_data.iloc[i]['x']
            dy = self.fingertip_data.iloc[i]['y'] - self.wrist_data.iloc[i]['y']
            dz = self.fingertip_data.iloc[i]['z'] - self.wrist_data.iloc[i]['z']
            
            # 計算與垂直軸的角度
            tool_angle = np.arctan2(np.sqrt(dx**2 + dy**2), dz) * 180 / np.pi
            
            tool_data.append({
                'frame': self.wrist_data.iloc[i]['frame'],
                'timestamp': self.wrist_data.iloc[i]['timestamp'],
                'tool_angle': tool_angle,
                'direction_x': dx,
                'direction_y': dy,
                'direction_z': dz
            })
            
        return pd.DataFrame(tool_data)
    
    def calculate_kinematics(self, data, fps=30):
        """計算運動學參數"""
        dt = 1.0 / fps
        
        # 計算速度
        data['vx'] = np.gradient(data['x']) / dt
        data['vy'] = np.gradient(data['y']) / dt  
        data['vz'] = np.gradient(data['z']) / dt
        data['velocity'] = np.sqrt(data['vx']**2 + data['vy']**2 + data['vz']**2)
        
        # 計算加速度
        data['ax'] = np.gradient(data['vx']) / dt
        data['ay'] = np.gradient(data['vy']) / dt
        data['az'] = np.gradient(data['vz']) / dt
        data['acceleration'] = np.sqrt(data['ax']**2 + data['ay']**2 + data['az']**2)
        
        # 計算躍度 (jerk)
        data['jerk'] = np.gradient(data['acceleration']) / dt
        
        # 計算移動距離
        data['distance'] = np.sqrt(np.diff(data['x'], prepend=data['x'].iloc[0])**2 + 
                                 np.diff(data['y'], prepend=data['y'].iloc[0])**2 + 
                                 np.diff(data['z'], prepend=data['z'].iloc[0])**2)
        
        # 平滑化處理
        if len(data) > 5:
            data['velocity_smooth'] = savgol_filter(data['velocity'], 5, 3)
            data['acceleration_smooth'] = savgol_filter(data['acceleration'], 5, 3)
        
        return data
        
    def detect_work_cycles(self, velocity_threshold=0.01, min_cycle_duration=3.0):
        """檢測作業循環"""
        print("檢測作業循環...")
        
        # 使用速度閾值檢測靜止點
        wrist_kinematic = self.calculate_kinematics(self.wrist_data.copy())
        
        # 找出低速度區間作為循環分割點
        low_velocity = wrist_kinematic['velocity'] < velocity_threshold
        
        # 找出速度從低到高的轉換點
        cycle_starts = []
        for i in range(1, len(low_velocity)):
            if not low_velocity.iloc[i-1] and low_velocity.iloc[i]:  # 開始靜止
                continue
            if low_velocity.iloc[i-1] and not low_velocity.iloc[i]:  # 結束靜止
                cycle_starts.append(i)
        
        # 根據最小循環時間過濾
        filtered_starts = [0]  # 從第一個frame開始
        for start in cycle_starts:
            time_diff = wrist_kinematic.iloc[start]['timestamp'] - wrist_kinematic.iloc[filtered_starts[-1]]['timestamp']
            if time_diff >= min_cycle_duration:
                filtered_starts.append(start)
        
        # 創建序列標記
        sequences = []
        for i, start in enumerate(filtered_starts[:-1]):
            end = filtered_starts[i+1] if i+1 < len(filtered_starts) else len(wrist_kinematic)
            for j in range(start, end):
                sequences.append(i+1)
        
        # 處理最後一個序列
        if len(sequences) < len(wrist_kinematic):
            last_seq = sequences[-1] if sequences else 1
            while len(sequences) < len(wrist_kinematic):
                sequences.append(last_seq)
        
        wrist_kinematic['sequence'] = sequences
        self.processed_data = wrist_kinematic
        
        print(f"檢測到 {max(sequences)} 個作業循環")
        return self.processed_data
    
    def analyze_trajectory(self):
        """手部軌跡分析"""
        if self.processed_data is None:
            self.detect_work_cycles()
            
        results = {}
        
        # 1. 3D軌跡路徑圖
        fig = plt.figure(figsize=(15, 5))
        
        # 子圖1: 3D軌跡
        ax1 = fig.add_subplot(131, projection='3d')
        sequences = self.processed_data['sequence'].unique()
        colors = plt.cm.Set3(np.linspace(0, 1, len(sequences)))
        
        for i, seq in enumerate(sequences):
            seq_data = self.processed_data[self.processed_data['sequence'] == seq]
            ax1.plot(seq_data['x'], seq_data['y'], seq_data['z'], 
                    color=colors[i], label=f'循環 {seq}', linewidth=2)
        
        ax1.set_xlabel('X 座標')
        ax1.set_ylabel('Y 座標') 
        ax1.set_zlabel('Z 座標')
        ax1.set_title('3D 手部移動軌跡')
        ax1.legend()
        
        # 子圖2: 移動距離統計
        ax2 = fig.add_subplot(132)
        seq_distances = []
        for seq in sequences:
            seq_data = self.processed_data[self.processed_data['sequence'] == seq]
            total_distance = seq_data['distance'].sum()
            seq_distances.append(total_distance)
        
        ax2.bar(sequences, seq_distances, alpha=0.7, color='steelblue')
        ax2.set_xlabel('作業循環')
        ax2.set_ylabel('總移動距離')
        ax2.set_title('各循環移動距離統計')
        ax2.grid(True, alpha=0.3)
        
        # 子圖3: 工具角度分析
        ax3 = fig.add_subplot(133)
        if self.tool_data is not None:
            # 合併數據
            merged_data = pd.merge(self.processed_data[['frame', 'sequence']], 
                                 self.tool_data[['frame', 'tool_angle']], 
                                 on='frame', how='left')
            
            for seq in sequences:
                seq_angles = merged_data[merged_data['sequence'] == seq]['tool_angle']
                if not seq_angles.empty:
                    ax3.plot(seq_angles.values, label=f'循環 {seq}', alpha=0.7)
            
            ax3.set_xlabel('時間點')
            ax3.set_ylabel('工具角度 (度)')
            ax3.set_title('工具接近角度一致性')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # 統計結果
        results['avg_distance'] = np.mean(seq_distances)
        results['distance_std'] = np.std(seq_distances)
        results['distance_by_sequence'] = dict(zip(sequences, seq_distances))
        
        return results
    
    def analyze_fatigue(self, window_size=50):
        """疲勞檢測分析"""
        if self.processed_data is None:
            self.detect_work_cycles()
        
        # 滑動窗口分析
        windows = []
        for i in range(0, len(self.processed_data) - window_size, window_size):
            window = self.processed_data.iloc[i:i+window_size]
            
            windows.append({
                'time': window['timestamp'].mean(),
                'avg_velocity': window['velocity'].mean(),
                'avg_acceleration': window['acceleration'].mean(),
                'velocity_std': window['velocity'].std(),
                'acceleration_std': window['acceleration'].std(),
                'jerk_mean': window['jerk'].mean(),
                'smoothness': 1 / (1 + window['jerk'].std())  # 平滑度指標
            })
        
        fatigue_df = pd.DataFrame(windows)
        
        # 可視化
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 加速度變化
        axes[0, 0].plot(fatigue_df['time'], fatigue_df['avg_acceleration'], 'r-', linewidth=2)
        axes[0, 0].fill_between(fatigue_df['time'], 
                               fatigue_df['avg_acceleration'] - fatigue_df['acceleration_std'],
                               fatigue_df['avg_acceleration'] + fatigue_df['acceleration_std'],
                               alpha=0.3)
        axes[0, 0].set_title('動作加速度變化')
        axes[0, 0].set_ylabel('加速度')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 速度穩定性
        axes[0, 1].plot(fatigue_df['time'], fatigue_df['velocity_std'], 'b-', linewidth=2)
        axes[0, 1].set_title('速度穩定性')
        axes[0, 1].set_ylabel('速度標準差')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 動作平滑度
        axes[1, 0].plot(fatigue_df['time'], fatigue_df['smoothness'], 'g-', linewidth=2)
        axes[1, 0].set_title('動作平滑度 (值越高越平滑)')
        axes[1, 0].set_ylabel('平滑度指標')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 精準度衰減 (基於工具角度變異)
        if self.tool_data is not None:
            # 計算角度精準度
            precision_windows = []
            merged_data = pd.merge(self.processed_data[['frame', 'timestamp']], 
                                 self.tool_data[['frame', 'tool_angle']], 
                                 on='frame', how='left')
            
            for i in range(0, len(merged_data) - window_size, window_size):
                window = merged_data.iloc[i:i+window_size]
                if not window['tool_angle'].isna().all():
                    angle_std = window['tool_angle'].std()
                    precision = 1 / (1 + angle_std)  # 精準度指標
                    precision_windows.append({
                        'time': window['timestamp'].mean(),
                        'precision': precision * 100
                    })
            
            if precision_windows:
                precision_df = pd.DataFrame(precision_windows)
                axes[1, 1].plot(precision_df['time'], precision_df['precision'], 'm-', linewidth=2)
                axes[1, 1].set_title('精準度衰減趨勢')
                axes[1, 1].set_ylabel('精準度 (%)')
                axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return fatigue_df
    
    def analyze_sop_compliance(self):
        """SOP合規性分析"""
        if self.processed_data is None:
            self.detect_work_cycles()
        
        sequences = self.processed_data['sequence'].unique()
        compliance_results = []
        
        # 標準參考值 (需根據實際SOP調整)
        STANDARD_CYCLE_TIME = 30.0  # 標準週期時間 (秒)
        STANDARD_TOOL_ANGLE = 90.0  # 標準工具角度 (度)
        STANDARD_MAX_DISTANCE = 0.5  # 標準最大移動距離
        
        for seq in sequences:
            seq_data = self.processed_data[self.processed_data['sequence'] == seq]
            
            if len(seq_data) < 2:
                continue
                
            # 計算週期時間
            cycle_time = seq_data['timestamp'].iloc[-1] - seq_data['timestamp'].iloc[0]
            
            # 計算總移動距離
            total_distance = seq_data['distance'].sum()
            
            # 計算動作平滑度
            smoothness = 1 / (1 + seq_data['jerk'].std()) if seq_data['jerk'].std() > 0 else 1
            
            # 工具角度分析
            angle_deviation = 0
            if self.tool_data is not None:
                seq_tool_data = pd.merge(seq_data[['frame']], 
                                       self.tool_data[['frame', 'tool_angle']], 
                                       on='frame', how='left')
                if not seq_tool_data['tool_angle'].isna().all():
                    angle_deviation = abs(seq_tool_data['tool_angle'].mean() - STANDARD_TOOL_ANGLE)
            
            compliance_results.append({
                'sequence': seq,
                'cycle_time': cycle_time,
                'time_compliance': min(100, (STANDARD_CYCLE_TIME / cycle_time) * 100),
                'total_distance': total_distance,
                'distance_efficiency': max(0, 100 - (total_distance - STANDARD_MAX_DISTANCE) * 200),
                'smoothness': smoothness * 100,
                'angle_deviation': angle_deviation,
                'angle_precision': max(0, 100 - angle_deviation * 2),
                'overall_score': 0  # 後面計算
            })
        
        compliance_df = pd.DataFrame(compliance_results)
        
        # 計算綜合評分
        if not compliance_df.empty:
            compliance_df['overall_score'] = (
                compliance_df['time_compliance'] * 0.3 + 
                compliance_df['distance_efficiency'] * 0.3 + 
                compliance_df['smoothness'] * 0.2 + 
                compliance_df['angle_precision'] * 0.2
            )
        
        # 可視化
        if not compliance_df.empty:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # 週期時間分析
            axes[0, 0].bar(compliance_df['sequence'], compliance_df['cycle_time'], alpha=0.7)
            axes[0, 0].axhline(y=STANDARD_CYCLE_TIME, color='r', linestyle='--', label='標準時間')
            axes[0, 0].set_title('作業週期時間分析')
            axes[0, 0].set_ylabel('時間 (秒)')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            
            # 角度偏差分析
            axes[0, 1].plot(compliance_df['sequence'], compliance_df['angle_deviation'], 'ro-')
            axes[0, 1].set_title('工具角度偏差分析')
            axes[0, 1].set_ylabel('偏差角度 (度)')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 移動效率
            axes[1, 0].bar(compliance_df['sequence'], compliance_df['distance_efficiency'], alpha=0.7, color='green')
            axes[1, 0].set_title('移動效率分析')
            axes[1, 0].set_ylabel('效率分數 (%)')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 綜合評分
            axes[1, 1].plot(compliance_df['sequence'], compliance_df['overall_score'], 'bo-', linewidth=2, markersize=8)
            axes[1, 1].fill_between(compliance_df['sequence'], compliance_df['overall_score'], alpha=0.3)
            axes[1, 1].set_title('SOP 綜合合規評分')
            axes[1, 1].set_ylabel('合規分數 (%)')
            axes[1, 1].set_ylim(0, 100)
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.show()
        
        return compliance_df
    
    def analyze_consistency(self):
        """品質一致性分析"""
        if self.processed_data is None:
            self.detect_work_cycles()
        
        sequences = self.processed_data['sequence'].unique()
        
        # 收集各項指標
        metrics = {
            'cycle_times': [],
            'total_distances': [], 
            'avg_velocities': [],
            'avg_accelerations': [],
            'smoothness_scores': [],
            'angle_precisions': []
        }
        
        for seq in sequences:
            seq_data = self.processed_data[self.processed_data['sequence'] == seq]
            
            if len(seq_data) < 2:
                continue
            
            # 週期時間
            cycle_time = seq_data['timestamp'].iloc[-1] - seq_data['timestamp'].iloc[0]
            metrics['cycle_times'].append(cycle_time)
            
            # 總移動距離
            total_distance = seq_data['distance'].sum()
            metrics['total_distances'].append(total_distance)
            
            # 平均速度和加速度
            metrics['avg_velocities'].append(seq_data['velocity'].mean())
            metrics['avg_accelerations'].append(seq_data['acceleration'].mean())
            
            # 平滑度
            smoothness = 1 / (1 + seq_data['jerk'].std()) if seq_data['jerk'].std() > 0 else 1
            metrics['smoothness_scores'].append(smoothness)
            
            # 角度精度
            if self.tool_data is not None:
                seq_tool_data = pd.merge(seq_data[['frame']], 
                                       self.tool_data[['frame', 'tool_angle']], 
                                       on='frame', how='left')
                if not seq_tool_data['tool_angle'].isna().all():
                    angle_precision = 1 / (1 + seq_tool_data['tool_angle'].std())
                    metrics['angle_precisions'].append(angle_precision)
                else:
                    metrics['angle_precisions'].append(0.5)
            else:
                metrics['angle_precisions'].append(0.5)
        
        # 計算一致性統計
        consistency_stats = {}
        for metric_name, values in metrics.items():
            if values:
                consistency_stats[metric_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'cv': np.std(values) / np.mean(values) if np.mean(values) != 0 else 0,  # 變異係數
                    'consistency_score': max(0, 100 - np.std(values) / np.mean(values) * 100) if np.mean(values) != 0 else 0
                }
        
        # 可視化
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 雷達圖 - 品質一致性
        if consistency_stats:
            categories = ['週期時間', '移動距離', '平均速度', '加速度', '動作平滑度', '角度精度']
            consistency_scores = [
                consistency_stats.get('cycle_times', {}).get('consistency_score', 0),
                consistency_stats.get('total_distances', {}).get('consistency_score', 0),
                consistency_stats.get('avg_velocities', {}).get('consistency_score', 0),
                consistency_stats.get('avg_accelerations', {}).get('consistency_score', 0),
                consistency_stats.get('smoothness_scores', {}).get('consistency_score', 0),
                consistency_stats.get('angle_precisions', {}).get('consistency_score', 0)
            ]
            
            # 雷達圖設置
            angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
            consistency_scores += consistency_scores[:1]  # 閉合圖形
            angles += angles[:1]
            
            ax_radar = fig.add_subplot(221, projection='polar')
            ax_radar.plot(angles, consistency_scores, 'o-', linewidth=2, color='blue')
            ax_radar.fill(angles, consistency_scores, alpha=0.25, color='blue')
            ax_radar.set_xticks(angles[:-1])
            ax_radar.set_xticklabels(categories)
            ax_radar.set_ylim(0, 100)
            ax_radar.set_title('品質一致性雷達圖')
            ax_radar.grid(True)
        
        # 週期時間變異
        if metrics['cycle_times']:
            axes[0, 1].plot(range(1, len(metrics['cycle_times'])+1), metrics['cycle_times'], 'bo-')
            axes[0, 1].axhline(y=np.mean(metrics['cycle_times']), color='r', linestyle='--', label='平均值')
            axes[0, 1].fill_between(range(1, len(metrics['cycle_times'])+1), 
                                  np.mean(metrics['cycle_times']) - np.std(metrics['cycle_times']),
                                  np.mean(metrics['cycle_times']) + np.std(metrics['cycle_times']),
                                  alpha=0.3, color='red', label='±1σ')
            axes[0, 1].set_title('週期時間一致性')
            axes[0, 1].set_ylabel('時間 (秒)')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # 移動距離變異
        if metrics['total_distances']:
            axes[1, 0].bar(range(1, len(metrics['total_distances'])+1), metrics['total_distances'], alpha=0.7)
            axes[1, 0].axhline(y=np.mean(metrics['total_distances']), color='r', linestyle='--', label='平均值')
            axes[1, 0].set_title('移動距離一致性')
            axes[1, 0].set_ylabel('距離')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # 變異係數比較
        if consistency_stats:
            metric_names = ['週期時間', '移動距離', '平均速度', '動作平滑度']
            cvs = [
                consistency_stats.get('cycle_times', {}).get('cv', 0),
                consistency_stats.get('total_distances', {}).get('cv', 0), 
                consistency_stats.get('avg_velocities', {}).get('cv', 0),
                consistency_stats.get('smoothness_scores', {}).get('cv', 0)
            ]
            
            axes[1, 1].barh(metric_names, cvs, color=['blue', 'green', 'orange', 'purple'])
            axes[1, 1].set_title('各指標變異係數 (越小越一致)')
            axes[1, 1].set_xlabel('變異係數')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return consistency_stats, metrics
    
    def generate_report(self):
        """生成綜合分析報告"""
        print("\n" + "="*60)
        print("SOP 作業分析報告")
        print("="*60)
        
        if self.processed_data is None:
            self.detect_work_cycles()
        
        # 基本統計
        total_frames = len(self.processed_data)
        total_sequences = len(self.processed_data['sequence'].unique())
        total_time = self.processed_data['timestamp'].iloc[-1] - self.processed_data['timestamp'].iloc[0]
        
        print(f"\n基本統計:")
        print(f"- 總影格數: {total_frames}")
        print(f"- 作業循環數: {total_sequences}")
        print(f"- 總分析時間: {total_time:.2f} 秒")
        print(f"- 平均循環時間: {total_time/total_sequences:.2f} 秒")
        
        # 執行各項分析
        print(f"\n正在執行各項分析...")
        
        trajectory_results = self.analyze_trajectory()
        print(f"✓ 軌跡分析完成")
        
        fatigue_results = self.analyze_fatigue()
        print(f"✓ 疲勞檢測完成")
        
        compliance_results = self.analyze_sop_compliance()
        print(f"✓ SOP合規性分析完成")
        
        consistency_stats, consistency_metrics = self.analyze_consistency()
        print(f"✓ 一致性分析完成")
        
        # 綜合建議
        print(f"\n分析建議:")
        if not compliance_results.empty:
            avg_score = compliance_results['overall_score'].mean()
            if avg_score >= 80:
                print("✓ 整體作業表現良好")
            elif avg_score >= 60:
                print("⚠ 作業表現中等，建議關注改善點")
            else:
                print("❌ 作業表現需要改善")

            # 具體建議
            if compliance_results['time_compliance'].mean() < 70:
                print("- 建議優化作業流程以提升時間效率")
            if compliance_results['angle_precision'].mean() < 70:
                print("- 建議加強工具操作角度的一致性訓練")
            if compliance_results['smoothness'].mean() < 70:
                print("- 建議改善動作流暢度，減少不必要的急停急起")

        print("\n" + "="*60)

        # 詳細改善方案
        print("\n改善方案建議:")

        # 時間效率改善
        if not compliance_results.empty and compliance_results['time_compliance'].mean() < 70:
            print("\n⏰ 時間效率改善:")
            print("  1. 制定標準作業程序(SOP)以減少不必要的等待時間")
            print("  2. 實施預防性維護計畫，避免設備故障造成的延誤")
            print("  3. 定期檢討作業流程，識別並消除瓶頸點")
            print("  4. 考慮引入自動化設備以提升整體效率")

        # 角度精度改善
        if not compliance_results.empty and compliance_results['angle_precision'].mean() < 70:
            print("\n📐 角度精度改善:")
            print("  1. 加強操作人員的技能訓練，特別是精密操作技巧")
            print("  2. 定期校正測量設備，確保測量精度")
            print("  3. 建立角度標準參考點，提供操作人員參考")
            print("  4. 實施角度檢查機制，即時發現並修正偏差")

        # 動作流暢度改善
        if not compliance_results.empty and compliance_results['smoothness'].mean() < 70:
            print("\n🔄 動作流暢度改善:")
            print("  1. 優化設備控制參數，減少不必要的急停急起")
            print("  2. 實施漸進式加減速控制策略")
            print("  3. 定期檢查機械部件，確保運行順暢")
            print("  4. 訓練操作人員平穩操作技巧")

        # 品質控制建議
        print("\n🎯 品質控制建議:")
        quality_score = compliance_results['quality_score'].mean() if not compliance_results.empty and 'quality_score' in compliance_results.columns else 0
        if quality_score < 80:
            print("  1. 建立多階段品質檢查點")
            print("  2. 實施統計製程控制(SPC)監控")
            print("  3. 定期進行品質稽核和改善檢討")
            print("  4. 建立不合格品處理標準程序")

        # 安全性建議
        print("\n🛡️ 安全性建議:")
        safety_score = compliance_results['safety_compliance'].mean() if not compliance_results.empty and 'safety_compliance' in compliance_results.columns else 0
        if safety_score < 90:
            print("  1. 加強安全教育訓練，提升安全意識")
            print("  2. 定期檢查安全設備和防護裝置")
            print("  3. 建立安全事故報告和分析機制")
            print("  4. 實施安全稽核和持續改善計畫")

        # 培訓建議
        print("\n📚 培訓建議:")
        print("  1. 針對表現較差的項目制定專項培訓計畫")
        print("  2. 定期舉辦技能競賽，激發學習動機")
        print("  3. 建立師傅帶徒弟制度，經驗傳承")
        print("  4. 引入模擬訓練系統，提供安全的練習環境")

        # 績效追蹤
        print("\n📊 績效追蹤:")
        print("  1. 建立週期性評估機制，追蹤改善成效")
        print("  2. 設定明確的改善目標和時程")
        print("  3. 定期召開檢討會議，分享最佳實務")
        print("  4. 建立獎勵機制，鼓勵持續改善")

        print("\n" + "="*60)
        print("報告生成完成 | " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("="*60)
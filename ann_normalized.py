import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
import numpy as np
import pickle
import os
from coordinate_normalization import process_data_with_normalization

# ANN 模型 (調整為適應更多特徵)
class ANN_Normalized(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, output_dim)
        )

    def forward(self, x):
        return self.model(x)

print("🚀 使用座標正規化的 ANN 訓練")
print("=" * 50)

# 使用正規化處理數據
print("📊 處理訓練數據...")
X, y, df_processed = process_data_with_normalization("1.xlsx", has_labels=True)

print(f"正規化後特徵維度: {X.shape}")
print(f"標籤數量: {len(np.unique(y))}")

# 將標籤轉換為數值
label_encoder = LabelEncoder()
y_encoded = label_encoder.fit_transform(y)
print(f"標籤編碼對應: {dict(zip(label_encoder.classes_, range(len(label_encoder.classes_))))}")

# 標準化特徵 (在正規化基礎上進一步標準化)
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 數據分割
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

# 轉換為 PyTorch tensors
X_train = torch.tensor(X_train, dtype=torch.float32)
y_train = torch.tensor(y_train, dtype=torch.long)
X_test = torch.tensor(X_test, dtype=torch.float32)
y_test = torch.tensor(y_test, dtype=torch.long)

# 創建數據加載器
train_ds = TensorDataset(X_train, y_train)
train_loader = DataLoader(train_ds, batch_size=32, shuffle=True)

# 創建模型
model = ANN_Normalized(input_dim=X.shape[1], output_dim=len(label_encoder.classes_))
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)

print(f"\n🧠 模型結構:")
print(f"輸入維度: {X.shape[1]}")
print(f"輸出類別: {len(label_encoder.classes_)}")

# 訓練
print(f"\n🏃 開始訓練...")
for epoch in range(30):  # 增加訓練輪數
    model.train()
    total_loss = 0
    for xb, yb in train_loader:
        preds = model(xb)
        loss = criterion(preds, yb)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    
    avg_loss = total_loss / len(train_loader)
    if (epoch + 1) % 5 == 0:
        print(f"Epoch {epoch+1}, Average Loss: {avg_loss:.4f}")

# 測試準確率
model.eval()
with torch.no_grad():
    preds = model(X_test)
    predicted_labels = preds.argmax(dim=1)
    acc = (predicted_labels == y_test).float().mean()
    print(f"\n✅ Test Accuracy: {acc:.4f}")
    
    # 顯示一些預測結果
    print("\n預測結果範例:")
    for i in range(min(10, len(y_test))):
        true_label = label_encoder.inverse_transform([y_test[i]])[0]
        pred_label = label_encoder.inverse_transform([predicted_labels[i]])[0]
        confidence = torch.softmax(preds[i], dim=0).max().item()
        print(f"真實: {true_label:<15} 預測: {pred_label:<15} 信心度: {confidence:.3f}")

# 保存正規化版本的模型
os.makedirs("saved_models_normalized", exist_ok=True)

# 保存模型
torch.save(model.state_dict(), "saved_models_normalized/ann_normalized_weights.pth")
torch.save(model, "saved_models_normalized/ann_normalized_complete.pth")

# 保存預處理器
with open("saved_models_normalized/ann_normalized_label_encoder.pkl", "wb") as f:
    pickle.dump(label_encoder, f)

with open("saved_models_normalized/ann_normalized_scaler.pkl", "wb") as f:
    pickle.dump(scaler, f)

# 保存模型配置信息
model_info = {
    "input_dim": X.shape[1],
    "output_dim": len(label_encoder.classes_),
    "test_accuracy": acc.item(),
    "label_classes": label_encoder.classes_.tolist(),
    "model_type": "ANN_Normalized",
    "features_include_relative": True,
    "normalization_method": "shoulder_center_and_scale"
}

with open("saved_models_normalized/ann_normalized_info.pkl", "wb") as f:
    pickle.dump(model_info, f)

print(f"\n💾 正規化 ANN 模型已保存到 'saved_models_normalized' 目錄:")
print(f"   - ann_normalized_weights.pth (模型權重)")
print(f"   - ann_normalized_complete.pth (完整模型)")
print(f"   - ann_normalized_label_encoder.pkl (標籤編碼器)")
print(f"   - ann_normalized_scaler.pkl (特徵標準化器)")
print(f"   - ann_normalized_info.pkl (模型配置信息)")

print(f"\n🎯 改進重點:")
print(f"   ✅ 座標正規化 - 消除位置偏移")
print(f"   ✅ 身體尺度正規化 - 消除大小差異")
print(f"   ✅ 相對特徵 - 添加關節間距離")
print(f"   ✅ 更深網路 - 增加模型容量")
print(f"   ✅ Dropout - 防止過擬合")

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder

# ANN 模型類別定義（需要與訓練時相同）
class ANN(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, output_dim)
        )

    def forward(self, x):
        return self.model(x)

# CNN 模型類別定義（需要與訓練時相同）
class CNN1D(nn.Module):
    def __init__(self, input_length, num_classes):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv1d(1, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
        )
        
        # 計算卷積後的特徵長度
        conv_output_length = input_length // 8  # 經過3次MaxPool1d(2)
        
        self.fc = nn.Sequential(
            nn.Linear(64 * conv_output_length, 128),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        x = self.conv(x)
        x = x.view(x.size(0), -1)  # 展平成全連接層輸入
        return self.fc(x)

def load_ann_model():
    """載入 ANN 模型"""
    print("🔄 載入 ANN 模型...")
    
    # 載入模型配置信息
    with open("saved_models/ann_model_info.pkl", "rb") as f:
        model_info = pickle.load(f)
    
    # 載入標籤編碼器和標準化器
    with open("saved_models/ann_label_encoder.pkl", "rb") as f:
        label_encoder = pickle.load(f)
    
    with open("saved_models/ann_scaler.pkl", "rb") as f:
        scaler = pickle.load(f)
    
    # 重建模型結構
    model = ANN(model_info["input_dim"], model_info["output_dim"])
    
    # 載入模型權重
    model.load_state_dict(torch.load("saved_models/ann_model_weights.pth"))
    model.eval()
    
    print(f"✅ ANN 模型載入成功！測試準確率: {model_info['test_accuracy']:.4f}")
    return model, label_encoder, scaler, model_info

def load_cnn_model():
    """載入 CNN 模型"""
    print("🔄 載入 CNN 模型...")
    
    # 載入模型配置信息
    with open("saved_models/cnn_model_info.pkl", "rb") as f:
        model_info = pickle.load(f)
    
    # 載入標籤編碼器和標準化器
    with open("saved_models/cnn_label_encoder.pkl", "rb") as f:
        label_encoder = pickle.load(f)
    
    with open("saved_models/cnn_scaler.pkl", "rb") as f:
        scaler = pickle.load(f)
    
    # 重建模型結構
    model = CNN1D(model_info["input_length"], model_info["num_classes"])
    
    # 載入模型權重
    model.load_state_dict(torch.load("saved_models/cnn_model_weights.pth"))
    model.eval()
    
    print(f"✅ CNN 模型載入成功！測試準確率: {model_info['test_accuracy']:.4f}")
    return model, label_encoder, scaler, model_info

def predict_with_ann(data_file, model, label_encoder, scaler):
    """使用 ANN 模型進行預測"""
    print(f"\n🎯 使用 ANN 模型預測 {data_file}...")
    
    # 讀取數據
    df = pd.read_excel(data_file)
    X = df.drop(["標籤", "幀數", "影片時間"], axis=1).values
    
    # 標準化
    X = scaler.transform(X)
    
    # 轉換為 tensor
    X_tensor = torch.tensor(X, dtype=torch.float32)
    
    # 預測
    with torch.no_grad():
        predictions = model(X_tensor)
        predicted_labels = predictions.argmax(dim=1)
    
    # 轉換回標籤名稱
    predicted_names = label_encoder.inverse_transform(predicted_labels.numpy())
    
    return predicted_names

def predict_with_cnn(data_file, model, label_encoder, scaler):
    """使用 CNN 模型進行預測"""
    print(f"\n🎯 使用 CNN 模型預測 {data_file}...")
    
    # 讀取數據
    df = pd.read_excel(data_file)
    X = df.drop(["標籤", "幀數", "影片時間"], axis=1).values
    
    # 標準化
    X = scaler.transform(X)
    
    # 為CNN重新整形數據
    X = X[:, np.newaxis, :]  # 增加 channel 維度
    
    # 轉換為 tensor
    X_tensor = torch.tensor(X, dtype=torch.float32)
    
    # 預測
    with torch.no_grad():
        predictions = model(X_tensor)
        predicted_labels = predictions.argmax(dim=1)
    
    # 轉換回標籤名稱
    predicted_names = label_encoder.inverse_transform(predicted_labels.numpy())
    
    return predicted_names

if __name__ == "__main__":
    print("🚀 模型載入和預測示例")
    print("=" * 50)
    
    try:
        # 載入 ANN 模型
        ann_model, ann_label_encoder, ann_scaler, ann_info = load_ann_model()
        
        # 載入 CNN 模型
        cnn_model, cnn_label_encoder, cnn_scaler, cnn_info = load_cnn_model()
        
        # 使用 1.xlsx 進行預測示例
        print(f"\n📊 對 1.xlsx 前10筆數據進行預測:")
        
        # ANN 預測
        ann_predictions = predict_with_ann("1.xlsx", ann_model, ann_label_encoder, ann_scaler)
        
        # CNN 預測
        cnn_predictions = predict_with_cnn("1.xlsx", cnn_model, cnn_label_encoder, cnn_scaler)
        
        # 讀取真實標籤
        df = pd.read_excel("1.xlsx")
        true_labels = df["標籤"].values
        
        # 顯示前10筆預測結果
        print("\n預測結果對比:")
        print("-" * 80)
        print(f"{'序號':<4} {'真實標籤':<15} {'ANN預測':<15} {'CNN預測':<15}")
        print("-" * 80)
        
        for i in range(min(10, len(true_labels))):
            print(f"{i+1:<4} {true_labels[i]:<15} {ann_predictions[i]:<15} {cnn_predictions[i]:<15}")
        
        print(f"\n✅ 預測完成！")
        
    except FileNotFoundError as e:
        print(f"❌ 檔案未找到: {e}")
        print("請先運行 ann.py 和 cnn.py 來訓練並保存模型")
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import pickle
from enhanced_model import EnhancedANN, process_enhanced_data

def load_enhanced_model():
    """載入增強模型"""
    print("🔄 載入增強 ANN 模型...")
    
    # 載入模型配置信息
    with open("saved_models_enhanced/enhanced_model_info.pkl", "rb") as f:
        model_info = pickle.load(f)
    
    # 載入標籤編碼器和標準化器
    with open("saved_models_enhanced/enhanced_label_encoder.pkl", "rb") as f:
        label_encoder = pickle.load(f)
    
    with open("saved_models_enhanced/enhanced_scaler.pkl", "rb") as f:
        scaler = pickle.load(f)
    
    # 重建模型結構
    model = EnhancedANN(model_info["input_dim"], model_info["output_dim"])
    
    # 載入模型權重
    model.load_state_dict(torch.load("saved_models_enhanced/enhanced_ann_weights.pth"))
    model.eval()
    
    print(f"✅ 增強模型載入成功！測試準確率: {model_info['test_accuracy']:.4f}")
    print(f"   特徵維度: {model_info['input_dim']}")
    print(f"   增強特徵: {model_info.get('features', [])}")
    
    return model, label_encoder, scaler, model_info

def predict_2xlsx_enhanced():
    """使用增強模型對 2.xlsx 進行預測"""
    print(f"\n📊 使用增強特徵處理 2.xlsx...")
    
    # 使用增強特徵處理 2.xlsx
    X, df_processed = process_enhanced_data("2.xlsx", has_labels=False)
    print(f"增強後特徵維度: {X.shape}")
    
    # 載入增強模型
    model, label_encoder, scaler, model_info = load_enhanced_model()
    
    # 標準化特徵 (使用訓練時的 scaler)
    print(f"\n🎯 使用增強模型進行預測...")
    X_scaled = scaler.transform(X)
    X_tensor = torch.tensor(X_scaled, dtype=torch.float32)
    
    # 預測
    with torch.no_grad():
        predictions = model(X_tensor)
        predicted_labels = predictions.argmax(dim=1)
        predicted_names = label_encoder.inverse_transform(predicted_labels.numpy())
        confidence = torch.softmax(predictions, dim=1).max(dim=1)[0]
    
    # 讀取原始時間信息
    df_original = pd.read_excel("2.xlsx")
    
    # 創建結果 DataFrame
    results_df = pd.DataFrame({
        '幀數': df_original['幀數'],
        '影片時間': df_original['影片時間'],
        '增強預測': predicted_names,
        '信心度': confidence.numpy()
    })
    
    # 保存預測結果
    results_df.to_excel("2xlsx_enhanced_predictions.xlsx", index=False)
    print(f"\n💾 增強預測結果已保存到 '2xlsx_enhanced_predictions.xlsx'")
    
    # 顯示統計信息
    print(f"\n📈 增強預測結果統計:")
    print(f"總數據筆數: {len(results_df)}")
    
    print(f"\n增強模型預測分布:")
    pred_counts = pd.Series(predicted_names).value_counts()
    for action, count in pred_counts.head(10).items():
        avg_confidence = results_df[results_df['增強預測'] == action]['信心度'].mean()
        print(f"  {action}: {count} 筆 ({count/len(results_df)*100:.1f}%) - 平均信心度: {avg_confidence:.3f}")
    
    # 顯示前20筆預測結果
    print(f"\n🔍 前20筆增強預測結果:")
    print("-" * 80)
    print(f"{'幀數':<6} {'時間':<12} {'增強預測':<20} {'信心度':<8}")
    print("-" * 80)
    
    for i in range(min(20, len(results_df))):
        row = results_df.iloc[i]
        print(f"{row['幀數']:<6} {row['影片時間']:<12} {row['增強預測']:<20} {row['信心度']:<8.3f}")
    
    # 計算高信心度預測比例
    high_confidence = (confidence > 0.9).sum().item()
    high_confidence_rate = high_confidence / len(results_df) * 100
    print(f"\n🎯 高信心度預測 (>0.9): {high_confidence}/{len(results_df)} ({high_confidence_rate:.1f}%)")
    
    return results_df

def compare_all_predictions():
    """比較所有模型的預測結果"""
    try:
        # 讀取所有預測結果
        original_df = pd.read_excel("2xlsx_predictions.xlsx")
        normalized_df = pd.read_excel("2xlsx_normalized_predictions.xlsx")
        enhanced_df = pd.read_excel("2xlsx_enhanced_predictions.xlsx")
        
        print(f"\n🔄 比較所有模型預測結果:")
        print("-" * 120)
        print(f"{'幀數':<6} {'原始ANN':<15} {'正規化ANN':<15} {'增強ANN':<15} {'一致性':<10}")
        print("-" * 120)
        
        for i in range(min(20, len(original_df))):
            original_pred = original_df.iloc[i]['ANN預測']
            normalized_pred = normalized_df.iloc[i]['正規化預測']
            enhanced_pred = enhanced_df.iloc[i]['增強預測']
            
            # 計算一致性
            predictions = [original_pred, normalized_pred, enhanced_pred]
            unique_preds = len(set(predictions))
            if unique_preds == 1:
                consistency = "全一致"
            elif unique_preds == 2:
                consistency = "部分一致"
            else:
                consistency = "不一致"
            
            print(f"{i:<6} {original_pred:<15} {normalized_pred:<15} {enhanced_pred:<15} {consistency:<10}")
        
        # 計算整體一致性
        total_full_agreement = 0
        total_partial_agreement = 0
        
        for i in range(len(original_df)):
            predictions = [
                original_df.iloc[i]['ANN預測'],
                normalized_df.iloc[i]['正規化預測'],
                enhanced_df.iloc[i]['增強預測']
            ]
            unique_preds = len(set(predictions))
            if unique_preds == 1:
                total_full_agreement += 1
            elif unique_preds == 2:
                total_partial_agreement += 1
        
        full_rate = total_full_agreement / len(original_df) * 100
        partial_rate = total_partial_agreement / len(original_df) * 100
        
        print(f"\n🤝 整體預測一致性:")
        print(f"   全部一致: {total_full_agreement}/{len(original_df)} ({full_rate:.1f}%)")
        print(f"   部分一致: {total_partial_agreement}/{len(original_df)} ({partial_rate:.1f}%)")
        
        # 分析動作序列的合理性
        print(f"\n📊 動作序列分析 (前50幀):")
        enhanced_sequence = enhanced_df['增強預測'].head(50).tolist()
        
        # 統計動作轉換
        transitions = {}
        for i in range(len(enhanced_sequence) - 1):
            current = enhanced_sequence[i]
            next_action = enhanced_sequence[i + 1]
            transition = f"{current} → {next_action}"
            transitions[transition] = transitions.get(transition, 0) + 1
        
        print("主要動作轉換:")
        for transition, count in sorted(transitions.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {transition}: {count} 次")
        
    except FileNotFoundError as e:
        print(f"\n⚠️  未找到某些預測結果檔案: {e}")

if __name__ == "__main__":
    print("🚀 使用增強模型對 2.xlsx 進行預測")
    print("=" * 60)
    
    try:
        # 進行增強預測
        results = predict_2xlsx_enhanced()
        
        # 比較所有預測結果
        compare_all_predictions()
        
        print(f"\n✅ 增強預測完成！")
        print(f"\n🎯 增強模型的優勢:")
        print(f"   ✅ 最高測試準確率 (97.17%)")
        print(f"   ✅ 座標正規化 + 相對特徵")
        print(f"   ✅ 時序特徵 (速度、加速度)")
        print(f"   ✅ 關節角度變化特徵")
        print(f"   ✅ BatchNorm + 早停機制")
        print(f"   ✅ 更深的網路結構")
        
    except FileNotFoundError as e:
        print(f"❌ 檔案未找到: {e}")
        print("請確認已訓練並保存增強模型")
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        import traceback
        traceback.print_exc()

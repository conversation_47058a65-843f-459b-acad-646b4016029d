import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder

# ANN 模型類別定義（需要與訓練時相同）
class ANN(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, output_dim)
        )

    def forward(self, x):
        return self.model(x)

# CNN 模型類別定義（需要與訓練時相同）
class CNN1D(nn.Module):
    def __init__(self, input_length, num_classes):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv1d(1, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
        )
        
        # 計算卷積後的特徵長度
        conv_output_length = input_length // 8  # 經過3次MaxPool1d(2)
        
        self.fc = nn.Sequential(
            nn.Linear(64 * conv_output_length, 128),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        x = self.conv(x)
        x = x.view(x.size(0), -1)  # 展平成全連接層輸入
        return self.fc(x)

def load_models():
    """載入兩個模型"""
    print("🔄 載入訓練好的模型...")
    
    # 載入 ANN 模型
    with open("saved_models/ann_model_info.pkl", "rb") as f:
        ann_info = pickle.load(f)
    with open("saved_models/ann_label_encoder.pkl", "rb") as f:
        ann_label_encoder = pickle.load(f)
    with open("saved_models/ann_scaler.pkl", "rb") as f:
        ann_scaler = pickle.load(f)
    
    ann_model = ANN(ann_info["input_dim"], ann_info["output_dim"])
    ann_model.load_state_dict(torch.load("saved_models/ann_model_weights.pth"))
    ann_model.eval()
    
    # 載入 CNN 模型
    with open("saved_models/cnn_model_info.pkl", "rb") as f:
        cnn_info = pickle.load(f)
    with open("saved_models/cnn_label_encoder.pkl", "rb") as f:
        cnn_label_encoder = pickle.load(f)
    with open("saved_models/cnn_scaler.pkl", "rb") as f:
        cnn_scaler = pickle.load(f)
    
    cnn_model = CNN1D(cnn_info["input_length"], cnn_info["num_classes"])
    cnn_model.load_state_dict(torch.load("saved_models/cnn_model_weights.pth"))
    cnn_model.eval()
    
    print(f"✅ 模型載入成功！")
    print(f"   ANN 測試準確率: {ann_info['test_accuracy']:.4f}")
    print(f"   CNN 測試準確率: {cnn_info['test_accuracy']:.4f}")
    
    return (ann_model, ann_label_encoder, ann_scaler), (cnn_model, cnn_label_encoder, cnn_scaler)

def predict_2xlsx():
    """對 2.xlsx 進行預測"""
    print(f"\n📊 讀取 2.xlsx 數據...")
    
    # 讀取數據
    df = pd.read_excel("2.xlsx")
    print(f"數據形狀: {df.shape}")
    print(f"欄位: {df.columns.tolist()[:10]}...")
    
    # 移除非數值特徵欄位（注意 2.xlsx 沒有 "標籤" 欄位）
    # 根據觀察，2.xlsx 有 "影片秒數" 而不是 "標籤"
    columns_to_remove = ["幀數", "影片秒數", "影片時間"]
    
    # 檢查哪些欄位實際存在
    existing_columns_to_remove = [col for col in columns_to_remove if col in df.columns]
    print(f"移除的欄位: {existing_columns_to_remove}")
    
    X = df.drop(existing_columns_to_remove, axis=1).values
    print(f"特徵維度: {X.shape}")
    
    # 載入模型
    (ann_model, ann_label_encoder, ann_scaler), (cnn_model, cnn_label_encoder, cnn_scaler) = load_models()
    
    # ANN 預測
    print(f"\n🎯 使用 ANN 模型進行預測...")
    X_ann = ann_scaler.transform(X)
    X_ann_tensor = torch.tensor(X_ann, dtype=torch.float32)
    
    with torch.no_grad():
        ann_predictions = ann_model(X_ann_tensor)
        ann_predicted_labels = ann_predictions.argmax(dim=1)
        ann_predicted_names = ann_label_encoder.inverse_transform(ann_predicted_labels.numpy())
        ann_confidence = torch.softmax(ann_predictions, dim=1).max(dim=1)[0]
    
    # CNN 預測
    print(f"🎯 使用 CNN 模型進行預測...")
    X_cnn = cnn_scaler.transform(X)
    X_cnn = X_cnn[:, np.newaxis, :]  # 增加 channel 維度
    X_cnn_tensor = torch.tensor(X_cnn, dtype=torch.float32)
    
    with torch.no_grad():
        cnn_predictions = cnn_model(X_cnn_tensor)
        cnn_predicted_labels = cnn_predictions.argmax(dim=1)
        cnn_predicted_names = cnn_label_encoder.inverse_transform(cnn_predicted_labels.numpy())
        cnn_confidence = torch.softmax(cnn_predictions, dim=1).max(dim=1)[0]
    
    # 創建結果 DataFrame
    results_df = pd.DataFrame({
        '幀數': df['幀數'],
        '影片時間': df['影片時間'],
        'ANN預測': ann_predicted_names,
        'ANN信心度': ann_confidence.numpy(),
        'CNN預測': cnn_predicted_names,
        'CNN信心度': cnn_confidence.numpy()
    })
    
    # 保存預測結果
    results_df.to_excel("2xlsx_predictions.xlsx", index=False)
    print(f"\n💾 預測結果已保存到 '2xlsx_predictions.xlsx'")
    
    # 顯示統計信息
    print(f"\n📈 預測結果統計:")
    print(f"總數據筆數: {len(results_df)}")
    
    print(f"\nANN 預測分布:")
    ann_counts = pd.Series(ann_predicted_names).value_counts()
    for action, count in ann_counts.head(10).items():
        print(f"  {action}: {count} 筆 ({count/len(results_df)*100:.1f}%)")
    
    print(f"\nCNN 預測分布:")
    cnn_counts = pd.Series(cnn_predicted_names).value_counts()
    for action, count in cnn_counts.head(10).items():
        print(f"  {action}: {count} 筆 ({count/len(results_df)*100:.1f}%)")
    
    # 顯示前20筆預測結果
    print(f"\n🔍 前20筆預測結果:")
    print("-" * 100)
    print(f"{'幀數':<6} {'時間':<12} {'ANN預測':<15} {'信心度':<8} {'CNN預測':<15} {'信心度':<8}")
    print("-" * 100)
    
    for i in range(min(20, len(results_df))):
        row = results_df.iloc[i]
        print(f"{row['幀數']:<6} {row['影片時間']:<12} {row['ANN預測']:<15} {row['ANN信心度']:<8.3f} {row['CNN預測']:<15} {row['CNN信心度']:<8.3f}")
    
    # 計算模型一致性
    agreement = (ann_predicted_names == cnn_predicted_names).sum()
    agreement_rate = agreement / len(results_df) * 100
    print(f"\n🤝 模型預測一致性: {agreement}/{len(results_df)} ({agreement_rate:.1f}%)")
    
    return results_df

if __name__ == "__main__":
    print("🚀 對 2.xlsx 進行動作預測")
    print("=" * 60)
    
    try:
        results = predict_2xlsx()
        print(f"\n✅ 預測完成！結果已保存到 '2xlsx_predictions.xlsx'")
        
    except FileNotFoundError as e:
        print(f"❌ 檔案未找到: {e}")
        print("請確認 2.xlsx 存在，且已訓練並保存模型")
    except Exception as e:
        print(f"❌ 發生錯誤: {e}")
        import traceback
        traceback.print_exc()

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
import numpy as np
import pickle
import os
from coordinate_normalization import process_data_with_normalization

def add_temporal_features(df, window_size=3):
    """
    添加時序特徵：速度、加速度、動作變化率
    """
    print(f"🕒 添加時序特徵 (窗口大小: {window_size})...")
    
    df_enhanced = df.copy()
    
    # 獲取所有座標欄位
    coord_cols = [col for col in df.columns if any(suffix in col for suffix in ['_X座標', '_Y座標', '_Z座標'])]
    
    # 計算速度 (一階差分)
    for col in coord_cols:
        velocity_col = col.replace('座標', '速度')
        df_enhanced[velocity_col] = df[col].diff().fillna(0)
    
    # 計算加速度 (二階差分)
    for col in coord_cols:
        accel_col = col.replace('座標', '加速度')
        df_enhanced[accel_col] = df[col].diff().diff().fillna(0)
    
    # 計算移動平均 (平滑特徵)
    for col in coord_cols:
        smooth_col = col.replace('座標', '平滑')
        df_enhanced[smooth_col] = df[col].rolling(window=window_size, center=True).mean().fillna(df[col])
    
    # 計算關節間角度變化率
    joint_pairs = [
        ('左肩膀', '左手腕', '左手肘'),
        ('右肩膀', '右手腕', '右手肘'),
        ('左臀部', '左膝蓋', '左腳踝'),
        ('右臀部', '右膝蓋', '右腳踝')
    ]
    
    for joint1, joint2, joint3 in joint_pairs:
        # 計算三點角度
        x1_col, y1_col = f'{joint1}_X座標', f'{joint1}_Y座標'
        x2_col, y2_col = f'{joint2}_X座標', f'{joint2}_Y座標'
        x3_col, y3_col = f'{joint3}_X座標', f'{joint3}_Y座標'
        
        if all(col in df.columns for col in [x1_col, y1_col, x2_col, y2_col, x3_col, y3_col]):
            # 向量計算
            v1_x = df[x1_col] - df[x2_col]
            v1_y = df[y1_col] - df[y2_col]
            v2_x = df[x3_col] - df[x2_col]
            v2_y = df[y3_col] - df[y2_col]
            
            # 角度計算
            dot_product = v1_x * v2_x + v1_y * v2_y
            magnitude1 = np.sqrt(v1_x**2 + v1_y**2)
            magnitude2 = np.sqrt(v2_x**2 + v2_y**2)
            
            # 避免除零
            magnitude1 = magnitude1.replace(0, 1e-6)
            magnitude2 = magnitude2.replace(0, 1e-6)
            
            cos_angle = dot_product / (magnitude1 * magnitude2)
            cos_angle = np.clip(cos_angle, -1, 1)  # 確保在有效範圍內
            
            angle = np.arccos(cos_angle)
            angle_col = f'{joint1}_{joint2}_{joint3}_角度'
            df_enhanced[angle_col] = angle
            
            # 角度變化率
            angle_change_col = f'{joint1}_{joint2}_{joint3}_角度變化率'
            df_enhanced[angle_change_col] = angle.diff().fillna(0)
    
    print(f"✅ 添加了時序特徵，新特徵數: {df_enhanced.shape[1] - df.shape[1]}")
    return df_enhanced

# 增強版 ANN 模型
class EnhancedANN(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(0.1),
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, output_dim)
        )

    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.classifier(features)
        return output

def process_enhanced_data(file_path, has_labels=True):
    """
    處理數據並添加所有增強特徵
    """
    print(f"📂 處理增強數據: {file_path}")
    
    # 基礎正規化處理
    if has_labels:
        X_basic, y, df_processed = process_data_with_normalization(file_path, has_labels=True)
    else:
        X_basic, df_processed = process_data_with_normalization(file_path, has_labels=False)
        y = None
    
    # 添加時序特徵
    df_enhanced = add_temporal_features(df_processed)
    
    # 移除非數值欄位
    if has_labels:
        non_numeric_cols = ['幀數', '影片時間', '標籤']
    else:
        non_numeric_cols = ['幀數', '影片時間', '影片秒數']
    
    existing_non_numeric = [col for col in non_numeric_cols if col in df_enhanced.columns]
    X_enhanced = df_enhanced.drop(existing_non_numeric, axis=1).values
    
    print(f"增強後特徵維度: {X_enhanced.shape}")
    
    if has_labels:
        return X_enhanced, y, df_enhanced
    else:
        return X_enhanced, df_enhanced

print("🚀 訓練增強版 ANN 模型")
print("=" * 50)

# 處理增強數據
X, y, df_enhanced = process_enhanced_data("1.xlsx", has_labels=True)

# 標籤編碼
label_encoder = LabelEncoder()
y_encoded = label_encoder.fit_transform(y)

# 特徵標準化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 數據分割
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

# 轉換為 PyTorch tensors
X_train = torch.tensor(X_train, dtype=torch.float32)
y_train = torch.tensor(y_train, dtype=torch.long)
X_test = torch.tensor(X_test, dtype=torch.float32)
y_test = torch.tensor(y_test, dtype=torch.long)

# 創建數據加載器
train_ds = TensorDataset(X_train, y_train)
train_loader = DataLoader(train_ds, batch_size=64, shuffle=True)

# 創建增強模型
model = EnhancedANN(input_dim=X.shape[1], output_dim=len(label_encoder.classes_))
criterion = nn.CrossEntropyLoss()
optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)

print(f"\n🧠 增強模型結構:")
print(f"輸入維度: {X.shape[1]}")
print(f"輸出類別: {len(label_encoder.classes_)}")
print(f"模型參數數量: {sum(p.numel() for p in model.parameters())}")

# 訓練
print(f"\n🏃 開始訓練...")
best_acc = 0
patience_counter = 0

for epoch in range(50):
    model.train()
    total_loss = 0
    for xb, yb in train_loader:
        preds = model(xb)
        loss = criterion(preds, yb)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    
    avg_loss = total_loss / len(train_loader)
    
    # 驗證
    model.eval()
    with torch.no_grad():
        val_preds = model(X_test)
        val_acc = (val_preds.argmax(dim=1) == y_test).float().mean()
    
    scheduler.step(avg_loss)
    
    if val_acc > best_acc:
        best_acc = val_acc
        patience_counter = 0
        # 保存最佳模型
        torch.save(model.state_dict(), "best_enhanced_model.pth")
    else:
        patience_counter += 1
    
    if (epoch + 1) % 10 == 0:
        print(f"Epoch {epoch+1}, Loss: {avg_loss:.4f}, Val Acc: {val_acc:.4f}, Best: {best_acc:.4f}")
    
    # 早停
    if patience_counter >= 15:
        print(f"早停於 epoch {epoch+1}")
        break

# 載入最佳模型
model.load_state_dict(torch.load("best_enhanced_model.pth"))

# 最終測試
model.eval()
with torch.no_grad():
    preds = model(X_test)
    predicted_labels = preds.argmax(dim=1)
    acc = (predicted_labels == y_test).float().mean()
    print(f"\n✅ 最終測試準確率: {acc:.4f}")

# 保存增強模型
os.makedirs("saved_models_enhanced", exist_ok=True)

torch.save(model.state_dict(), "saved_models_enhanced/enhanced_ann_weights.pth")
torch.save(model, "saved_models_enhanced/enhanced_ann_complete.pth")

with open("saved_models_enhanced/enhanced_label_encoder.pkl", "wb") as f:
    pickle.dump(label_encoder, f)

with open("saved_models_enhanced/enhanced_scaler.pkl", "wb") as f:
    pickle.dump(scaler, f)

model_info = {
    "input_dim": X.shape[1],
    "output_dim": len(label_encoder.classes_),
    "test_accuracy": acc.item(),
    "label_classes": label_encoder.classes_.tolist(),
    "model_type": "EnhancedANN",
    "features": ["coordinate_normalization", "relative_features", "temporal_features", "angle_features"]
}

with open("saved_models_enhanced/enhanced_model_info.pkl", "wb") as f:
    pickle.dump(model_info, f)

print(f"\n💾 增強模型已保存")
print(f"\n🎯 增強特徵包括:")
print(f"   ✅ 座標正規化")
print(f"   ✅ 相對距離特徵")
print(f"   ✅ 速度和加速度")
print(f"   ✅ 關節角度變化")
print(f"   ✅ BatchNorm + 早停")

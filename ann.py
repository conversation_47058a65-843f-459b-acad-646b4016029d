import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
import numpy as np

# 資料讀取與預處理
df = pd.read_excel("1.xlsx")  # 讀取Excel檔案
print(f"數據形狀: {df.shape}")
print(f"標籤類別: {df['標籤'].unique()}")
print(f"標籤分布:\n{df['標籤'].value_counts()}")

# 移除非數值特徵欄位
X = df.drop(["標籤", "幀數", "影片時間"], axis=1).values  # 特徵
y = df["標籤"].values  # 標籤

# 將標籤轉換為數值
label_encoder = LabelEncoder()
y = label_encoder.fit_transform(y)
print(f"標籤編碼對應: {dict(zip(label_encoder.classes_, range(len(label_encoder.classes_))))}")

scaler = StandardScaler()
X = scaler.fit_transform(X)

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

X_train = torch.tensor(X_train, dtype=torch.float32)
y_train = torch.tensor(y_train, dtype=torch.long)
X_test = torch.tensor(X_test, dtype=torch.float32)
y_test = torch.tensor(y_test, dtype=torch.long)

train_ds = TensorDataset(X_train, y_train)
train_loader = DataLoader(train_ds, batch_size=32, shuffle=True)

# ANN 模型
class ANN(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, output_dim)
        )

    def forward(self, x):
        return self.model(x)

model = ANN(input_dim=X.shape[1], output_dim=len(label_encoder.classes_))
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 訓練
for epoch in range(20):
    model.train()
    for xb, yb in train_loader:
        preds = model(xb)
        loss = criterion(preds, yb)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
    print(f"Epoch {epoch+1}, Loss: {loss.item():.4f}")

# 測試準確率
model.eval()
with torch.no_grad():
    preds = model(X_test)
    predicted_labels = preds.argmax(dim=1)
    acc = (predicted_labels == y_test).float().mean()
    print(f"Test Accuracy: {acc:.4f}")

    # 顯示一些預測結果
    print("\n預測結果範例:")
    for i in range(min(10, len(y_test))):
        true_label = label_encoder.inverse_transform([y_test[i]])[0]
        pred_label = label_encoder.inverse_transform([predicted_labels[i]])[0]
        print(f"真實標籤: {true_label}, 預測標籤: {pred_label}")

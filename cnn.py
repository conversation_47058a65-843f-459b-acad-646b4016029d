import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder

# 資料讀取與預處理
df = pd.read_excel("1.xlsx")  # 讀取Excel檔案
print(f"數據形狀: {df.shape}")
print(f"標籤類別: {df['標籤'].unique()}")

# 移除非數值特徵欄位
X = df.drop(["標籤", "幀數", "影片時間"], axis=1).values  # 特徵
y = df["標籤"].values  # 標籤

# 將標籤轉換為數值
label_encoder = LabelEncoder()
y = label_encoder.fit_transform(y)
print(f"標籤編碼對應: {dict(zip(label_encoder.classes_, range(len(label_encoder.classes_))))}")

# 標準化特徵
scaler = StandardScaler()
X = scaler.fit_transform(X)

# 為CNN重新整形數據 - 將特徵視為1D序列
X = X[:, np.newaxis, :]  # 增加 channel 維度 → (N, 1, 132)

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

X_train = torch.tensor(X_train, dtype=torch.float32)
y_train = torch.tensor(y_train, dtype=torch.long)
X_test = torch.tensor(X_test, dtype=torch.float32)
y_test = torch.tensor(y_test, dtype=torch.long)

train_ds = TensorDataset(X_train, y_train)
train_loader = DataLoader(train_ds, batch_size=32, shuffle=True)

# 1D-CNN 模型
class CNN1D(nn.Module):
    def __init__(self, input_length, num_classes):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv1d(1, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
        )

        # 計算卷積後的特徵長度
        conv_output_length = input_length // 8  # 經過3次MaxPool1d(2)

        self.fc = nn.Sequential(
            nn.Linear(64 * conv_output_length, 128),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        x = self.conv(x)
        x = x.view(x.size(0), -1)  # 展平成全連接層輸入
        return self.fc(x)

model = CNN1D(input_length=X.shape[2], num_classes=len(label_encoder.classes_))
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 訓練
for epoch in range(20):
    model.train()
    for xb, yb in train_loader:
        preds = model(xb)
        loss = criterion(preds, yb)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
    print(f"Epoch {epoch+1}, Loss: {loss.item():.4f}")

# 測試
model.eval()
with torch.no_grad():
    preds = model(X_test)
    predicted_labels = preds.argmax(dim=1)
    acc = (predicted_labels == y_test).float().mean()
    print(f"Test Accuracy: {acc:.4f}")

    # 顯示一些預測結果
    print("\n預測結果範例:")
    for i in range(min(10, len(y_test))):
        true_label = label_encoder.inverse_transform([y_test[i]])[0]
        pred_label = label_encoder.inverse_transform([predicted_labels[i]])[0]
        print(f"真實標籤: {true_label}, 預測標籤: {pred_label}")

# 保存模型
import pickle
import os

# 創建模型保存目錄
os.makedirs("saved_models", exist_ok=True)

# 保存完整模型（包含結構和權重）
torch.save(model.state_dict(), "saved_models/cnn_model_weights.pth")
torch.save(model, "saved_models/cnn_model_complete.pth")

# 保存標籤編碼器和標準化器
with open("saved_models/cnn_label_encoder.pkl", "wb") as f:
    pickle.dump(label_encoder, f)

with open("saved_models/cnn_scaler.pkl", "wb") as f:
    pickle.dump(scaler, f)

# 保存模型配置信息
model_info = {
    "input_length": X.shape[2],
    "num_classes": len(label_encoder.classes_),
    "test_accuracy": acc.item(),
    "label_classes": label_encoder.classes_.tolist(),
    "model_type": "CNN1D"
}

with open("saved_models/cnn_model_info.pkl", "wb") as f:
    pickle.dump(model_info, f)

print(f"\n✅ CNN 模型已保存到 'saved_models' 目錄:")
print(f"   - cnn_model_weights.pth (模型權重)")
print(f"   - cnn_model_complete.pth (完整模型)")
print(f"   - cnn_label_encoder.pkl (標籤編碼器)")
print(f"   - cnn_scaler.pkl (特徵標準化器)")
print(f"   - cnn_model_info.pkl (模型配置信息)")

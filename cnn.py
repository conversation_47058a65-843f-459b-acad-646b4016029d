import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np

# 模擬序列資料（可替換為真實資料）
X = np.random.rand(1000, 100)  # 1000筆樣本，每筆100長度序列
y = np.random.randint(0, 2, size=(1000,))  # 二分類

X = X[:, np.newaxis, :]  # 增加 channel 維度 → (N, 1, 100)

X_train, X_test = X[:800], X[800:]
y_train, y_test = y[:800], y[800:]

X_train = torch.tensor(X_train, dtype=torch.float32)
y_train = torch.tensor(y_train, dtype=torch.long)
X_test = torch.tensor(X_test, dtype=torch.float32)
y_test = torch.tensor(y_test, dtype=torch.long)

train_ds = TensorDataset(X_train, y_train)
train_loader = DataLoader(train_ds, batch_size=32, shuffle=True)

# 1D-CNN 模型
class CNN1D(nn.Module):
    def __init__(self):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv1d(1, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
            nn.Conv1d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool1d(2),
        )
        self.fc = nn.Sequential(
            nn.Linear(32 * 25, 64),
            nn.ReLU(),
            nn.Linear(64, 2)  # 二分類
        )

    def forward(self, x):
        x = self.conv(x)
        x = x.view(x.size(0), -1)  # 展平成全連接層輸入
        return self.fc(x)

model = CNN1D()
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 訓練
for epoch in range(20):
    model.train()
    for xb, yb in train_loader:
        preds = model(xb)
        loss = criterion(preds, yb)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
    print(f"Epoch {epoch+1}, Loss: {loss.item():.4f}")

# 測試
model.eval()
with torch.no_grad():
    preds = model(X_test)
    acc = (preds.argmax(dim=1) == y_test).float().mean()
    print(f"Test Accuracy: {acc:.4f}")
